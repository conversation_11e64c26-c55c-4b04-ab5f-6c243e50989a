'''
Description: 
Date: 2025-06-26 11:45:27
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-06-26 16:18:27
FilePath: \Spider_tzb\config.py
'''
# -*- coding: utf-8 -*-
"""
南京欧美同学会爬虫配置文件
"""

# 多页面配置
PAGES = [
    {
        'url': 'https://www.njwrsa.org.cn/about/10.html',
        'name': '本会简介',
        'xpath': '/html/body/div[3]/div/div[2]/div/div[2]'
    },
    {
        'url': 'https://www.njwrsa.org.cn/about/11.html',
        'name': '本会章程',
        'xpath': '/html/body/div[3]/div/div[2]/div/div[2]'
    },
    {
        'url': 'https://www.njwrsa.org.cn/about/12.html',
        'name': '理事会名单',
        'xpath': '/html/body/div[3]/div/div[2]/div/div[2]'
    },
    {
        'url': 'https://www.njwrsa.org.cn/about/24.html',
        'name': '联系我们',
        'xpath': '/html/body/div[3]/div/div[2]/div/div[2]'
    },
]

# 输出文件配置
OUTPUT_FILENAME = "南京欧美同学会_资料.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试间隔（秒）

# SSL证书验证（如果网站证书有问题可以设置为False）
VERIFY_SSL = True

# 代理设置（如果需要使用代理，请取消注释并填写）
# PROXIES = {
#     'http': 'http://your-proxy:port',
#     'https': 'https://your-proxy:port'
# }
PROXIES = None

# OpenAI API配置 (硅基流动)
OPENAI_API_KEY = "5e776e1c-5a93-4fc1-ac5f-8135c3e5771b"  # 请替换为您的API密钥
OPENAI_BASE_URL = "https://api-inference.modelscope.cn/v1/chat/completions"  # 硅基流动API端点
OPENAI_MODEL = "Qwen/Qwen2.5-VL-72B-Instruct"  # 使用的模型

# 图片识别提示词
IMAGE_OCR_PROMPT = """请识别这张图片中的文字内容，这是南京欧美同学会理事会名单。
请按照以下格式整理输出：
- 如果是职务和姓名的列表，请保持原有格式
- 如果是表格，请用文本表格形式输出
- 保持所有人名和职务的准确性
- 如果有联系方式，也请一并提取

请直接输出识别的文字内容，不要添加额外说明。"""
