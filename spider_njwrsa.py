# -*- coding: utf-8 -*-
"""
南京欧美同学会网站爬虫
专门用于爬取"本会简介"页面内容并输出为Excel格式
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
from lxml import html, etree
import re
from datetime import datetime
import os
import sys
import time
import base64
from urllib.parse import urljoin
try:
    from openpyxl.styles import Alignment
except ImportError:
    Alignment = None
try:
    from openai import OpenAI
except ImportError:
    OpenAI = None
try:
    from PIL import Image
except ImportError:
    Image = None
from config import (PAGES, OUTPUT_FILENAME, HEADERS,
                    REQUEST_TIMEOUT, MAX_RETRIES, RETRY_DELAY, VERIFY_SSL, PROXIES,
                    OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MODEL, IMAGE_OCR_PROMPT)


class NJWRSASpider:
    """南京欧美同学会网站爬虫类"""

    def __init__(self):
        """初始化爬虫"""
        self.pages = PAGES
        self.headers = HEADERS
        self.timeout = REQUEST_TIMEOUT
        self.output_file = OUTPUT_FILENAME
        self.max_retries = MAX_RETRIES
        self.retry_delay = RETRY_DELAY
        self.verify_ssl = VERIFY_SSL
        self.proxies = PROXIES

        # 初始化OpenAI客户端
        if OpenAI and OPENAI_API_KEY != "your-api-key-here":
            self.openai_client = OpenAI(
                api_key=OPENAI_API_KEY,
                base_url=OPENAI_BASE_URL
            )
        else:
            self.openai_client = None
        
    def fetch_webpage(self, url):
        """
        获取网页内容（带重试机制）
        Args:
            url: 目标网址
        Returns:
            tuple: (是否成功, 网页内容/错误信息, 页面标题)
        """

        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    print(f"第 {attempt + 1} 次尝试...")
                    time.sleep(self.retry_delay)

                # 构建请求参数
                request_params = {
                    'headers': self.headers,
                    'timeout': self.timeout,
                    'verify': self.verify_ssl
                }

                # 如果配置了代理，添加代理设置，否则禁用代理
                if self.proxies:
                    request_params['proxies'] = self.proxies
                else:
                    # 明确禁用代理，避免系统代理干扰
                    request_params['proxies'] = {'http': None, 'https': None}

                response = requests.get(url, **request_params)
                response.raise_for_status()  # 检查HTTP错误

                # 尝试自动检测编码
                if response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding or 'utf-8'
                elif not response.encoding:
                    response.encoding = 'utf-8'

                # 获取页面标题
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.get_text().strip() if title else "未获取到标题"

                print(f"✓ 网页获取成功，页面标题: {page_title}")
                return True, response.text, page_title

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (超过{self.timeout}秒)"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except requests.exceptions.ConnectionError as e:
                error_msg = f"网络连接错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, "网络连接错误，请检查网络连接或防火墙设置", ""

            except requests.exceptions.SSLError as e:
                error_msg = f"SSL证书错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                print("提示: 如果是SSL证书问题，可以在config.py中设置VERIFY_SSL=False")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except requests.exceptions.HTTPError as e:
                error_msg = f"HTTP错误: {e}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except Exception as e:
                error_msg = f"获取网页时发生未知错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

        return False, "所有重试都失败了", ""
    
    def extract_content_by_xpath(self, html_content, xpath):
        """
        使用XPath提取内容
        Args:
            html_content: HTML内容
            xpath: XPath路径
        Returns:
            tuple: (是否成功, 提取的内容)
        """
        try:
            tree = html.fromstring(html_content)
            elements = tree.xpath(xpath)
            
            if not elements:
                return False, ""

            # 提取文本内容
            content_parts = []
            for element in elements:
                if hasattr(element, 'text_content'):
                    text = element.text_content()
                else:
                    text = str(element)
                content_parts.append(text)

            content = '\n'.join(content_parts)
            return True, content

        except Exception as e:
            return False, ""
    
    def extract_content_by_css(self, html_content):
        """使用CSS选择器作为备用方案提取内容"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            selectors = ['div.content', '.content', 'div[class*="content"]', 'body']

            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    content = '\n'.join([elem.get_text() for elem in elements])
                    if content.strip():
                        return True, content
            return False, ""
        except:
            return False, ""

    def clean_text(self, text):
        """清理文本内容"""
        if not text:
            return ""
        try:
            # 移除HTML标签和实体
            text = re.sub(r'<[^>]+>', '', text)
            text = text.replace('&nbsp;', ' ').replace('&lt;', '<').replace('&gt;', '>')
            text = text.replace('&amp;', '&').replace('&quot;', '"').replace('&#39;', "'")

            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'\n\s*\n', '\n\n', text)
            return text.strip()
        except:
            return text

    def extract_images_from_xpath(self, html_content, xpath, base_url):
        """从指定XPath提取图片URL"""
        try:
            tree = html.fromstring(html_content)
            img_elements = tree.xpath(f"{xpath}//img")

            print(f"  XPath查找到 {len(img_elements)} 个img元素")

            image_urls = []
            for i, img in enumerate(img_elements, 1):
                src = img.get('src')
                alt = img.get('alt', '')
                print(f"  图片{i}: src='{src}', alt='{alt}'")

                if src:
                    # 处理相对URL
                    full_url = urljoin(base_url, src)
                    image_urls.append(full_url)
                    print(f"  完整URL: {full_url}")
                else:
                    print(f"  图片{i}: 没有src属性")

            return image_urls
        except Exception as e:
            print(f"  提取图片URL失败: {str(e)}")
            return []

    def download_image(self, url):
        """下载图片并转换为base64"""
        print(f"  下载图片: {url}")

        try:
            # 为图片请求添加特殊的请求头
            img_headers = self.headers.copy()
            img_headers['Referer'] = url.split('/')[0] + '//' + url.split('/')[2] + '/'

            # 禁用代理（如果有的话）
            request_params = {
                'headers': img_headers,
                'timeout': self.timeout,
                'verify': self.verify_ssl,
                'proxies': {'http': None, 'https': None} if not self.proxies else self.proxies
            }

            response = requests.get(url, **request_params)
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"  警告: 响应不是图片类型: {content_type}")

            # 检查内容长度
            content_length = len(response.content)
            print(f"  图片大小: {content_length} 字节")

            if content_length == 0:
                print("  错误: 图片内容为空")
                return None

            return base64.b64encode(response.content).decode('utf-8')

        except requests.exceptions.RequestException as e:
            print(f"  下载失败: {str(e)}")
            return None
        except Exception as e:
            print(f"  处理失败: {str(e)}")
            return None

    def recognize_image_with_openai(self, base64_image):
        """使用OpenAI识别图片中的文字"""
        if not self.openai_client:
            return "OpenAI客户端未配置"

        # 重试机制
        for attempt in range(3):
            try:
                if attempt > 0:
                    print(f"    重试第 {attempt + 1} 次...")
                    time.sleep(2)

                response = self.openai_client.chat.completions.create(
                    model=OPENAI_MODEL,
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": IMAGE_OCR_PROMPT},
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                                }
                            ]
                        }
                    ],
                    max_tokens=2000,
                    timeout=60
                )
                return response.choices[0].message.content

            except Exception as e:
                error_msg = str(e)
                print(f"    API调用失败 (尝试 {attempt + 1}/3): {error_msg}")

                if attempt == 2:  # 最后一次尝试
                    return f"图片识别失败: {error_msg}"

        return "图片识别失败: 所有重试都失败了"

    def process_images_page(self, html_content, xpath, base_url):
        """处理包含图片的页面"""
        print("开始处理图片页面...")

        # 提取图片URL
        image_urls = self.extract_images_from_xpath(html_content, xpath, base_url)
        if not image_urls:
            print("未找到任何图片")
            return "未找到图片"

        print(f"找到 {len(image_urls)} 张图片")

        # 识别所有图片
        all_text = []
        success_count = 0

        for i, url in enumerate(image_urls, 1):
            print(f"\n处理图片 {i}/{len(image_urls)}")

            # 下载图片
            base64_image = self.download_image(url)
            if not base64_image:
                error_msg = f"图片 {i} 下载失败: {url}"
                print(f"  {error_msg}")
                all_text.append(error_msg)
                continue

            print(f"  图片下载成功，开始识别...")

            # 识别文字
            text = self.recognize_image_with_openai(base64_image)
            if text and not text.startswith("图片识别失败"):
                success_count += 1
                print(f"  识别成功 ({len(text)} 字符)")
            else:
                print(f"  识别失败: {text}")

            all_text.append(f"=== 图片 {i} ===\n{text}")

        result = "\n\n".join(all_text)
        print(f"\n图片处理完成: {success_count}/{len(image_urls)} 成功")
        return result

    def save_to_excel(self, results):
        """保存所有结果到Excel文件"""
        try:
            # 检查文件是否被占用
            if os.path.exists(self.output_file):
                try:
                    # 尝试重命名文件来检测是否被占用
                    temp_name = self.output_file + '.tmp'
                    os.rename(self.output_file, temp_name)
                    os.rename(temp_name, self.output_file)
                except OSError:
                    print(f"文件被占用，请关闭Excel文件: {self.output_file}")
                    return False

            with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                for result in results:
                    data = {
                        '标题': [result['title']],
                        '内容': [result['content']],
                        '爬取时间': [result['time']],
                        '来源网址': [result['url']]
                    }
                    df = pd.DataFrame(data)

                    # 确保工作表名称有效
                    sheet_name = result['name'].replace('/', '_').replace('\\', '_')[:31]
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 调整列宽
                    worksheet = writer.sheets[sheet_name]
                    worksheet.column_dimensions['A'].width = 20
                    worksheet.column_dimensions['B'].width = 80
                    worksheet.column_dimensions['C'].width = 20
                    worksheet.column_dimensions['D'].width = 50

                    # 设置自动换行
                    if Alignment:
                        for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):
                            for cell in row:
                                cell.alignment = Alignment(wrap_text=True, vertical='top')
            return True
        except Exception as e:
            print(f"保存Excel失败: {str(e)}")
            return False

    def run(self):
        """运行多页面爬虫"""
        print("南京欧美同学会网站爬虫启动")
        print(f"准备爬取 {len(self.pages)} 个页面")

        results = []
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        for i, page in enumerate(self.pages, 1):
            print(f"\n[{i}/{len(self.pages)}] 爬取: {page['name']}")

            # 获取网页
            success, html_content, page_title = self.fetch_webpage(page['url'])
            if not success:
                print(f"✗ 失败: {html_content}")
                continue

            # 特殊处理理事会名单页面（包含图片）
            if page['name'] == '理事会名单':
                content = self.process_images_page(html_content, page['xpath'], page['url'])
                if content == "未找到图片":
                    print("✗ 未找到图片")
                    continue
            else:
                # 普通文本页面处理
                success, content = self.extract_content_by_xpath(html_content, page['xpath'])
                if not success or not content.strip():
                    success, content = self.extract_content_by_css(html_content)
                    if not success or not content.strip():
                        print("✗ 内容提取失败")
                        continue

                # 清理文本
                content = self.clean_text(content)
                if not content.strip():
                    print("✗ 清理后内容为空")
                    continue

            results.append({
                'name': page['name'],
                'title': page_title,
                'content': content,
                'time': current_time,
                'url': page['url']
            })
            print(f"✓ 成功 ({len(content)} 字符)")

        # 保存结果
        if results:
            if self.save_to_excel(results):
                print(f"\n✓ 完成! 共爬取 {len(results)} 个页面，保存至: {self.output_file}")
                return True
            else:
                print("\n✗ 保存失败")
                return False
        else:
            print("\n✗ 没有成功爬取任何页面")
            return False


def main():
    """主函数"""
    try:
        spider = NJWRSASpider()
        success = spider.run()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n错误: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
