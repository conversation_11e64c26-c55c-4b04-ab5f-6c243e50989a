# 南京欧美同学会网站爬虫

## 项目简介

本项目是一个专门用于爬取南京欧美同学会官网"本会简介"页面内容的Python爬虫工具。爬取的内容将自动保存为Excel格式，便于后续处理和分析。

## 功能特点

- 🎯 **多页面爬取**: 同时爬取本会简介、本会章程、理事会名单三个页面
- 📊 **Excel输出**: 自动将爬取内容格式化并保存为Excel文件的不同工作表
- 🖼️ **图片识别**: 使用OpenAI API识别理事会名单页面中的图片文字
- 🔄 **备用方案**: 当XPath提取失败时，自动使用CSS选择器作为备用方案
- 🧹 **文本清理**: 自动清理多余的空白字符和特殊字符
- ⏰ **时间记录**: 记录爬取时间和来源网址
- 🛡️ **错误处理**: 完善的异常处理机制

## 目标网站

- **网站**: 南京欧美同学会官网
- **爬取页面**:
  - 本会简介: <https://www.njwrsa.org.cn/about/10.html>
  - 本会章程: <https://www.njwrsa.org.cn/about/11.html>
  - 理事会名单: <https://www.njwrsa.org.cn/about/12.html> (图片识别)

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置设置

### 1. OpenAI API配置

理事会名单页面需要使用图片识别功能，请在 `config.py` 中配置您的OpenAI API：

```python
# 在config.py中修改以下配置
OPENAI_API_KEY = "your-actual-api-key"  # 替换为您的API密钥
OPENAI_BASE_URL = "https://api.siliconflow.cn/v1"  # 硅基流动端点
```

### 2. 测试配置

运行配置测试脚本检查设置是否正确：

```bash
python test_config.py
```

## 使用方法

### 1. 直接运行

```bash
python spider_njwrsa.py
```

### 2. 配置修改

如需修改目标URL或XPath路径，请编辑 `config.py` 文件：

```python
# 修改目标URL
TARGET_URL = "你的目标网址"

# 修改XPath路径
CONTENT_XPATH = "你的xpath路径"

# 修改输出文件名
OUTPUT_FILENAME = "你的文件名.xlsx"
```

## 输出文件

爬虫运行成功后，会在当前目录生成 `南京欧美同学会_完整资料.xlsx` 文件，包含三个工作表：

- **本会简介**: 网站简介文字内容
- **本会章程**: 章程文字内容
- **理事会名单**: 通过AI识别的名单图片文字内容

每个工作表包含以下列：

| 列名 | 说明 |
|------|------|
| 标题 | 页面标题 |
| 内容 | 提取的正文内容或图片识别结果 |
| 爬取时间 | 数据获取时间 |
| 来源网址 | 原始页面URL |

## 项目结构

```
Spider_tzb/
├── spider_njwrsa.py      # 主爬虫脚本
├── config.py             # 配置文件
├── requirements.txt      # 依赖包列表
├── README.md            # 项目说明文档
└── 南京欧美同学会_本会简介.xlsx  # 输出文件（运行后生成）
```

## 技术栈

- **Python 3.7+**
- **requests**: HTTP请求库
- **BeautifulSoup4**: HTML解析库
- **pandas**: 数据处理库
- **openpyxl**: Excel文件操作库
- **lxml**: XML/HTML解析库

## 注意事项

1. 请确保网络连接正常
2. 目标网站可能有反爬虫机制，建议适当增加请求间隔
3. 如果XPath路径发生变化，请及时更新配置文件
4. 建议在使用前先测试网站的可访问性

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的robots.txt协议和使用条款。
